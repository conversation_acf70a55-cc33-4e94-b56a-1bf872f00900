package cn.com.chinastock.cnf.metrics;

import com.zaxxer.hikari.HikariDataSource;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.junit.jupiter.api.Test;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class GalaxyHikariMetricAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxyHikariMetricAutoConfiguration.class));

    @Test
    void should_set_prometheusMeterRegistry_when_property_is_enabled() {
        contextRunner
                .withPropertyValues("galaxy.metrics.datasource.prometheus.enabled=true")
                .withBean(HikariDataSource.class, HikariDataSource::new)
                .withBean(PrometheusMeterRegistry.class, () -> new PrometheusMeterRegistry(key -> null))
                .run(context -> {
                    // Verify MeterRegistry beans exist
                    Map<String, MeterRegistry> beans = context.getBeansOfType(MeterRegistry.class);

                    assertThat(beans).hasSize(2);
                    assertThat(beans.get("customPrometheusMeterRegistry")).isInstanceOf(PrometheusMeterRegistry.class);

                    // Verify MeterRegistryCustomizer exists
                    Map<String, MeterRegistryCustomizer> customizers = context.getBeansOfType(MeterRegistryCustomizer.class);
                    assertThat(customizers).hasSize(1);
                    assertThat(customizers.get("hikariMetricsCustomizer")).isNotNull();

                    // Note: HikariDataSource.getMetricRegistry() might be null in test context
                    // because MeterRegistryCustomizer is applied during registry initialization
                    HikariDataSource dataSource = context.getBean(HikariDataSource.class);
                    // We don't assert on dataSource.getMetricRegistry() here as it depends on timing
                });
    }



    @Test
    void should_update_pool_size_when_config_changes() {
        contextRunner
                .withPropertyValues(
                        "app.id=test-app",
                        "galaxy.datasource.dynamic-maximum-pool-size=true",
                        "spring.datasource.hikari.maximum-pool-size=20"
                )
                .withBean(HikariDataSource.class, () -> {
                    HikariDataSource ds = new HikariDataSource();
                    ds.setMaximumPoolSize(10);
                    return ds;
                })
                .run(context -> {
                    HikariDataSource dataSource = context.getBean(HikariDataSource.class);
                    assertThat(dataSource.getMaximumPoolSize()).isEqualTo(10);
                });
    }

    @Test
    void should_create_meterRegistryCustomizer_when_property_is_enabled() {
        contextRunner
                .withPropertyValues("galaxy.metrics.datasource.prometheus.enabled=true")
                .withBean(HikariDataSource.class, HikariDataSource::new)
                .withBean(PrometheusMeterRegistry.class, () -> new PrometheusMeterRegistry(key -> null))
                .run(context -> {
                    // Verify MeterRegistryCustomizer bean exists
                    Map<String, MeterRegistryCustomizer> customizers = context.getBeansOfType(MeterRegistryCustomizer.class);

                    assertThat(customizers).hasSize(1);
                    assertThat(customizers.get("hikariMetricsCustomizer")).isNotNull();
                });
    }

    @Test
    void should_not_create_beans_when_property_is_disabled() {
        contextRunner
                .withPropertyValues("galaxy.metrics.datasource.prometheus.enabled=false")
                .withBean(HikariDataSource.class, HikariDataSource::new)
                .withBean(PrometheusMeterRegistry.class, () -> new PrometheusMeterRegistry(key -> null))
                .run(context -> {
                    // Verify no custom beans are created when disabled
                    Map<String, MeterRegistryCustomizer> customizers = context.getBeansOfType(MeterRegistryCustomizer.class);
                    assertThat(customizers).isEmpty();

                    Map<String, MeterRegistry> registries = context.getBeansOfType(MeterRegistry.class);
                    // Only the provided PrometheusMeterRegistry should exist
                    assertThat(registries).hasSize(1);
                });
    }

}
