package cn.com.chinastock.cnf.metrics;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import com.zaxxer.hikari.HikariDataSource;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;

/**
 * 配置 Hikari 数据源的监控指标，通过日志和 Prometheus 输出。
 *
 * 注意：使用 @AutoConfigureAfter 确保在 DataSource 自动配置完成后再执行，
 * 避免 MeterFilter 在 Meter 注册后配置的警告。
 */
@Configuration
@EnableConfigurationProperties(GalaxyMetricsProperties.class)
@ConditionalOnClass({HikariDataSource.class})
@AutoConfigureAfter(DataSourceAutoConfiguration.class)
public class GalaxyHikariMetricAutoConfiguration {
    @Autowired
    private DataSource dataSource;

    /**
     * 使用 MeterRegistryCustomizer 来配置 HikariCP 指标，这是 Spring Boot 推荐的方式
     * 可以避免在 Meter 注册后配置 MeterFilter 的警告
     *
     * @return MeterRegistryCustomizer for PrometheusMeterRegistry
     */
    @Bean
    @ConditionalOnProperty(prefix = GalaxyMetricsProperties.CONFIG_PREFIX, name = "datasource.prometheus.enabled", havingValue = "true")
    @ConditionalOnClass({PrometheusMeterRegistry.class})
    public MeterRegistryCustomizer<PrometheusMeterRegistry> hikariMetricsCustomizer() {
        return registry -> {
            // 在 MeterRegistry 初始化时就配置 HikariCP 指标
            if (dataSource instanceof HikariDataSource hikariDataSource) {
                if (hikariDataSource.getMetricRegistry() == null) {
                    hikariDataSource.setMetricRegistry(registry);
                    GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "HikariCP MetricRegistry configured via MeterRegistryCustomizer: " + registry);
                }
            }

            // 处理动态数据源的情况
            if (dataSource.getClass().getName().equals("cn.com.chinastock.cnf.datasource.GalaxyDynamicDataSource") && dataSource instanceof AbstractRoutingDataSource) {
                var defaultSource = ((AbstractRoutingDataSource) dataSource).getResolvedDefaultDataSource();
                if (defaultSource instanceof HikariDataSource hikariDataSource) {
                    if (hikariDataSource.getMetricRegistry() == null) {
                        hikariDataSource.setMetricRegistry(registry);
                        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "HikariCP MetricRegistry configured for dynamic datasource via MeterRegistryCustomizer: " + registry);
                    }
                }
            }
        };
    }

}
