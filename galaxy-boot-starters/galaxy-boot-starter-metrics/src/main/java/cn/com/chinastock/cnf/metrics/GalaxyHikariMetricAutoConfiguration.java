package cn.com.chinastock.cnf.metrics;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import com.zaxxer.hikari.HikariDataSource;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import jakarta.inject.Singleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;

/**
 * 配置 Hikari 数据源的监控指标，通过日志和 Prometheus 输出。
 */
@Configuration
@EnableConfigurationProperties(GalaxyMetricsProperties.class)
@ConditionalOnClass({HikariDataSource.class})
public class GalaxyHikariMetricAutoConfiguration {
    @Autowired
    private DataSource dataSource;

    /**
     * 注册 PrometheusMeterRegistry，用于记录 Hikari 数据源的指标到 Prometheus
     *
     * @param prometheusMeterRegistry PrometheusMeterRegistry
     * @return PrometheusMeterRegistry
     */
    @Bean(name = "customPrometheusMeterRegistry")
    @Primary
    @ConditionalOnProperty(prefix = GalaxyMetricsProperties.CONFIG_PREFIX, name = "datasource.prometheus.enabled", havingValue = "true")
    @ConditionalOnClass({PrometheusMeterRegistry.class})
    public MeterRegistry customPrometheusMeterRegistry(PrometheusMeterRegistry prometheusMeterRegistry) {
        if (dataSource instanceof HikariDataSource hikariDataSource) {
            if (hikariDataSource.getMetricRegistry() == null) {
                hikariDataSource.setMetricRegistry(prometheusMeterRegistry);
            }

            GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "MetricRegistry: " + hikariDataSource.getMetricRegistry());
            return prometheusMeterRegistry;
        }

        // 如果数据源是动态数据源，默认的 dataSource 将是个 AbstractRoutingDataSource 类型，此时需要获取默认数据源
        if (dataSource.getClass().getName().equals("cn.com.chinastock.cnf.datasource.GalaxyDynamicDataSource") && dataSource instanceof AbstractRoutingDataSource) {
            var defaultSource = ((AbstractRoutingDataSource) dataSource).getResolvedDefaultDataSource();
            if (defaultSource instanceof HikariDataSource hikariDataSource) {
                if (hikariDataSource.getMetricRegistry() == null) {
                    hikariDataSource.setMetricRegistry(prometheusMeterRegistry);
                }

                GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "MetricRegistry: " + hikariDataSource.getMetricRegistry());
            }
        }

        return prometheusMeterRegistry;
    }

}
