# MeterFilter 警告问题修复说明

## 问题描述

在启动应用时，出现如下警告日志：

```
A MeterFilter is being configured after a Meter has been registered to this registry. 
All MeterFilters should be configured before any Meters are registered. 
If that is not possible or you have a use case where it should be allowed, 
let the Micrometer maintainers know at https://github.com/micrometer-metrics/micrometer/issues/4920.
```

## 问题根因

这个警告是由于 Spring Boot 自动配置的启动顺序问题导致的：

1. **Spring Boot Actuator** 先启动，创建 `PrometheusMeterRegistry`
2. **HikariCP DataSource** 初始化，可能已经注册了一些基础 meters
3. **`GalaxyHikariMetricAutoConfiguration`** 后执行，调用 `hikariDataSource.setMetricRegistry(prometheusMeterRegistry)`
4. 这时 Micrometer 检测到在已有 meters 的情况下配置 MeterFilter，产生警告

## 解决方案

### 1. 使用 @AutoConfigureAfter 注解

在 `GalaxyHikariMetricAutoConfiguration` 类上添加 `@AutoConfigureAfter(DataSourceAutoConfiguration.class)` 注解，确保在数据源自动配置完成后再执行。

### 2. 使用 MeterRegistryCustomizer（推荐）

Spring Boot 官方推荐使用 `MeterRegistryCustomizer` 来配置 MeterRegistry，这样可以在 MeterRegistry 初始化时就配置相关设置，避免后续配置 MeterFilter 的问题。

```java
@Bean
@ConditionalOnProperty(prefix = GalaxyMetricsProperties.CONFIG_PREFIX, name = "datasource.prometheus.enabled", havingValue = "true")
@ConditionalOnClass({PrometheusMeterRegistry.class})
public MeterRegistryCustomizer<PrometheusMeterRegistry> hikariMetricsCustomizer() {
    return registry -> {
        // 在 MeterRegistry 初始化时就配置 HikariCP 指标
        if (dataSource instanceof HikariDataSource hikariDataSource) {
            if (hikariDataSource.getMetricRegistry() == null) {
                hikariDataSource.setMetricRegistry(registry);
                GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "HikariCP MetricRegistry configured via MeterRegistryCustomizer: " + registry);
            }
        }
        // ... 处理动态数据源的情况
    };
}
```

## 配置说明

当同时启用以下配置时会触发此问题：

```yaml
management:
  prometheus:
    metrics:
      export:
        enabled: true

galaxy:
  metrics:
    datasource:
      prometheus:
        enabled: true
```

## 修复效果

修复后：
- 消除了 MeterFilter 警告日志
- HikariCP 指标仍然正常工作
- 使用了 Spring Boot 推荐的配置方式
- 保持了向后兼容性

## 参考资料

- [Micrometer Issue #4920](https://github.com/micrometer-metrics/micrometer/issues/4920)
- [Spring Boot Metrics Documentation](https://docs.spring.io/spring-boot/reference/actuator/metrics.html)
- [Spring Boot MeterRegistryCustomizer](https://docs.spring.io/spring-boot/3.5.4/api/java/org/springframework/boot/actuate/autoconfigure/metrics/MeterRegistryCustomizer.html)
